<script setup lang="ts">
import DashboardTopbar from './DashboardTopbar.vue'
import DashboardSidebar from './DashboardSidebar.vue'
import { useLayoutStore } from '~/stores/layout'

const { layoutConfig } = useLayoutStore()
const { lineState, isSidebarActive, resetLine } = useProductline()
const outsideClickListener = ref<any | null>(null)

watch(isSidebarActive, (newVal) => {
  if (newVal) {
    bindOutsideClickListener()
  }
  else {
    unbindOutsideClickListener()
  }
})

const containerClass = computed(() => {
  return {
    'layout-overlay': layoutConfig.menuMode === 'overlay',
    'layout-static': layoutConfig.menuMode === 'static',
    'layout-static-inactive': lineState.staticLineDesktopInactive && layoutConfig.menuMode === 'static',
    'layout-overlay-active': lineState.overlayLineActive,
    'layout-mobile-active': lineState.staticLineMobileActive,
  }
})

function bindOutsideClickListener() {
  if (!outsideClickListener.value) {
    outsideClickListener.value = (event: PointerEvent) => {
      if (isOutsideClicked(event)) {
        resetLine()
      }
    }
    document.addEventListener('click', outsideClickListener.value)
  }
}

function unbindOutsideClickListener() {
  if (outsideClickListener.value) {
    document.removeEventListener('click', outsideClickListener.value)
    outsideClickListener.value = null
  }
}

function isOutsideClicked(event: PointerEvent) {
  const sidebarEl = document.querySelector('.layout-sidebar')
  const topbarEl = document.querySelector('.layout-menu-button')
  if (event.target) {
    return !(sidebarEl?.isSameNode(event.target as Element) || sidebarEl?.contains(event.target as Element) || topbarEl?.isSameNode(event.target as Element) || topbarEl?.contains(event.target as Element))
  }
}
</script>

<template>
  <div class="layout-wrapper" :class="containerClass">
    <DashboardTopbar />
    <DashboardSidebar />
    <div class="layout-main-container">
      <div class="layout-main">
        <router-view v-slot="{ Component }">
          <Transition name="fade" type="animation">
            <component :is="Component" />
          </Transition>
        </router-view>
      </div>
    </div>
    <div class="layout-mask animate-fade-in" />
  </div>
</template>

<style lang="css" scoped>
.fade-enter-active {
  animation: fade-in 0.5s;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
