@layer reset,preflights, primevue,primeicon, icons,utilities;

@import '@unocss/reset/tailwind.css' layer(reset);
@import 'primeicons/primeicons.css' layer(primeicon);
@import './layout/layout.css';

html,
body,
#app {
  font-size: 14px;
  height: 100vh;
  overflow: overlay;
  margin: 0;
  padding: 0;
}

::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
  background: var(--p-surface-100);
  border: 1px solid var(--p-surface-100);
}

.dark ::-webkit-scrollbar-track {
  background: var(--p-surface-800);
  border: 1px solid var(--p-surface-700);
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: var(--p-surface-200);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--p-surface-700);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-300);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-600);
}
