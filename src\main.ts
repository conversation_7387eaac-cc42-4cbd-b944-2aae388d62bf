import Aura from '@primeuix/themes/aura'
import { HTTPError } from 'ky'
import { createPinia } from 'pinia'
import { createPersistedStatePlugin } from 'pinia-plugin-persistedstate-2'
import PrimeVue from 'primevue/config'
import Ripple from 'primevue/ripple'
import StyleClass from 'primevue/styleclass'
import ToastService from 'primevue/toastservice'
import type { ComponentPublicInstance } from 'vue'
import { createApp } from 'vue'
// 引入echarts
import * as echarts from 'echarts'
import ConfirmationService from 'primevue/confirmationservice'
import Echarts from 'vue-echarts'
import App from './App.vue'
import { setupPermissionDirective } from './plugins/permission'
import { router } from '~/router/index'
// 引入样式 mian在前，unocss在后
import './styles/main.css'

import 'uno.css'

const installPersistedStatePlugin = createPersistedStatePlugin()
const pinia = createPinia()
pinia.use(context => installPersistedStatePlugin(context))
const app = createApp(App)

app.use(PrimeVue, {
  theme: {
    ripper: true,
    preset: Aura,
    options: {
      darkModeSelector: '.dark',
      cssLayer: {
        name: 'primevue',
      },
    },
  },
})
// 使用组件
app.component('e-charts', Echarts)
// 全局挂载 echarts
app.config.globalProperties.$echarts = echarts
app.use(ToastService)
app.use(ConfirmationService)
app.directive('styleclass', StyleClass)
app.directive('ripple', Ripple)
// app.use(Casdoor, config)
app.use(pinia)
app.use(router)
app.config.errorHandler = (err: unknown, instance: ComponentPublicInstance | null, info: string) => {
  if (err instanceof HTTPError) {
    errorToast({
      summary: err.name,
      detail: err.message,
      life: 3000,
    })
  }
  else {
    errorToast({
      summary: info,
      detail: err as string,
      life: 3000,

    })
  }
}

setupPermissionDirective(app)

app.mount('#app')
