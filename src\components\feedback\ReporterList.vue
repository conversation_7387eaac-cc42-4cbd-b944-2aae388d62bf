<script setup lang="ts">
import type { Reporter } from '~/api/feedback/config/type'

const props = defineProps<{
  name: string
  removable?: boolean
  onRemove?: (index: number) => void
}>()

const { remove, fields } = useFieldArray<Reporter>(() => props.name)
const { errorMessage } = useField(() => props.name)
</script>

<template>
  <div class="max-h-full flex flex-wrap gap-1 overflow-auto">
    <Chip
      v-for="(user, i) in fields"
      :key="i"
      :label="user.value.reportName"
      class="border-1 border-surface"
      :removable="removable"
    >
      <template #removeicon>
        <i class="pi pi-minus-circle cursor-pointer" @click="remove(i)" />
      </template>
    </Chip>
    <ErrorMsg :error-message="errorMessage" />
  </div>
</template>
