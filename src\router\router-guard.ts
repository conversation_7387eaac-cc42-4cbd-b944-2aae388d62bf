import { HTTPError } from 'ky'
import type { Router } from 'vue-router'

import { useMenuStore } from '~/stores/menu'
import { useUserStore } from '~/stores/user'

export const loginPath = '/login'

// todo: optimize logic
export function globalGuard(router: Router) {
  router.beforeEach(async (to, _) => {
    const token = localStorage.getItem(import.meta.env.VITE_APP_ACCESS_STORE)
    const { menu, fetchMenu, setActiveMenuItemByPath } = useMenuStore()
    // 未登录
    if (!token) {
      // 未登录，且访问的不是公开页面或登录页，跳转到登录页
      if (!to.meta.isPublic && loginPath !== to.path) {
        return loginPath
      }
      return true
    }
    // 无菜单，查询菜单
    if (menu.length === 0) {
      await fetchMenu()
    }
    if (to.path === loginPath) {
      return true
    }

    const userStore = useUserStore()
    // 无用户，获取用户信息
    if (!userStore.user && token && !to.meta.isPublic) {
      try {
        await userStore.fetchUser()
      }
      catch (ex) {
        if (ex instanceof HTTPError) {
          if (ex.response.status === 401) {
            return '/401'
          }
          else {
            return '/500'
          }
        }
      }
    }
    // 公开页面
    if (to.meta.isPublic) {
      // 根据当前路由设置激活的菜单项
      setActiveMenuItemByPath(to.path)
      return true
    }
    // 无访问权限
    const { hasAccess } = useAccess()
    if (hasAccess(to.meta.access)) {
      // 根据当前路由设置激活的菜单项
      setActiveMenuItemByPath(to.path)
      return true
    }
    else {
      return '/403'
    }
  })
}
