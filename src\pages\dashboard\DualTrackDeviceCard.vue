<script setup lang="ts">
import { deviceApi } from '~/api/device'

interface Props {
  device: {
    code: string
    type: string
    actualCt: number
    theoreticalCt: number
    trackName: string
    bottleneck: boolean
  }
}

const props = defineProps<Props>()
const deviceStatus = ref<number>(0)

async function fetchDeviceStatus() {
  try {
    const { status } = await deviceApi.getClientStatus(props.device.code)
    deviceStatus.value = status
  }
  catch (error) {
    console.error('Failed to fetch device status:', error)
  }
}

onMounted(() => {
  fetchDeviceStatus()
})
</script>

<template>
  <div class="relative flex-1 border-2 p-2">
    <span
      class="absolute right-0.5 top-1 h-2 w-2 rounded-full"
      :class="deviceStatus === 1 ? 'bg-green-500' : 'bg-red-500'"
    />
    <div class="grid grid-cols-10 w-full items-center">
      <!-- 设备代码 & 类型 -->
      <span class="col-span-6 text-xs text-gray-700 dark:text-gray-200">{{ device.code }}</span>
      <span class="col-span-4 justify-self-end text-xs text-gray-500 dark:text-gray-400">
        {{ device.type }}
      </span>

      <!-- 实际CT -->
      <span class="col-span-5 text-sm text-gray-500 dark:text-gray-400">实际CT:</span>
      <span
        class="col-span-5 justify-self-end text-sm font-600"
        :class="device.bottleneck ? 'text-red-500' : 'text-orange-500 dark:text-orange-200'"
      >
        {{ device.actualCt }}s
      </span>

      <!-- 理论CT -->
      <span class="col-span-5 text-sm text-gray-500 dark:text-gray-400">理论CT:</span>
      <span class="col-span-5 justify-self-end text-sm text-gray-700 font-600 dark:text-gray-200">
        {{ device.theoreticalCt }}s
      </span>
    </div>
  </div>
</template>
