<script setup lang="ts">
import DeviceCtCard from './DeviceCtCard.vue'
import DualTrackDeviceCard from './DualTrackDeviceCard.vue'
import { useAnalyticsSearchSchema } from './schema'
import { analyzeApi } from '~/api/analyze'
import type { AlarmInfo, DefectTypeResult, DeviceCtInfo, HourlyOutputList, OeeResult } from '~/api/analyze/type'
import { TriggerRecordApi } from '~/api/feedback/trigger'
import DefectTypeChart from '~/components/charts/DefectTypeChart.vue'
import HourlyOutputChart from '~/components/charts/HourlyOutputChart.vue'
import Top5DefectsChart from '~/components/charts/Top5DefectsChart.vue'
import { useDashboardStore } from '~/stores/dashboard'

const props = defineProps<{
  code: string
}>()

// 状态管理
const dashboardStore = useDashboardStore()
const form = useAnalyticsSearchSchema()
const loading = ref<boolean>(false)

// 数据状态
const oee = ref<OeeResult>()
const devicesCt = ref<DeviceCtInfo[]>()
const hourlyOutput = ref<HourlyOutputList>()
const defectTypes = ref<DefectTypeResult>()
const topAlarms = ref<AlarmInfo[]>()
const productionLineType = ref<string>('')
const refreshInterval = ref<ReturnType<typeof setInterval>>()

// 计算属性
const deviceChunks = computed(() => {
  const devices = devicesCt.value ?? []
  const chunkSize = 5
  return Array.from({ length: Math.ceil(devices.length / chunkSize) }, (_, i) => devices.slice(i * chunkSize, (i + 1) * chunkSize))
})

// Add this after other computed properties
const devicesByTrack = computed(() => {
  const grouped = new Map<string, DeviceCtInfo[]>()
  devicesCt.value?.forEach((device) => {
    if (!grouped.has(device.trackName)) {
      grouped.set(device.trackName, [])
    }
    grouped.get(device.trackName)?.push(device)
  })
  return grouped
})

// 日期处理方法
function initializeDates() {
  const now = new Date()
  const currentDay = new Date(now)
  const nextDay = new Date(now)
  nextDay.setDate(now.getDate() + 1)

  const startTime = new Date(currentDay.setHours(8, 0, 0, 0))
  const endTime = new Date(nextDay.setHours(8, 0, 0, 0))

  setStartTime(startTime)
  setEndTime(endTime)
}

// 检查并更新日期
function checkAndUpdateDates() {
  const now = new Date()
  const endTime = form.values.endTime
  if (!endTime || now > endTime) {
    initializeDates()
  }
}

// API 调用封装
async function safeApiCall(apiFunction: () => Promise<any>, setter: (data: any) => void) {
  try {
    const result = await apiFunction()
    setter(result)
  }
  catch (error) {
    console.error('API call failed:', error)
  }
}

// 搜索方法
const search = form.handleSubmit(async (values) => {
  loading.value = true

  await Promise.allSettled([
    safeApiCall(() => analyzeApi.getLineOee(values), data => oee.value = data),
    safeApiCall(() => analyzeApi.getDevicesCt(values), data => devicesCt.value = data),
    safeApiCall(() => analyzeApi.getHourlyOutput(values), data => hourlyOutput.value = data),
    safeApiCall(() => analyzeApi.getDefectTypes(values), data => defectTypes.value = data),
    safeApiCall(() => analyzeApi.getTopAlarms(values), data => topAlarms.value = data),
    safeApiCall(() => analyzeApi.getCurrentProduct(values), (data) => {
      dashboardStore.setCurrentProduct(data.productionLineInfos)
      productionLineType.value = data.productionLineType
    }),
    safeApiCall(() => TriggerRecordApi.latestOpenException(values.code), (data) => {
      dashboardStore.setException(data.anomaliesName)
    }),
    safeApiCall(() => analyzeApi.getServerTime(), data => dashboardStore.setCurrentTime(
      new Date(data).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      }).replace(/\//g, '-'),
    )),
  ])

  loading.value = false
})

// 生命周期和监听器
watch(() => props.code, (newCode) => {
  if (newCode) {
    // 设置产品线
    form.setFieldValue('code', newCode)
    // 初始化日期
    initializeDates()
    // 搜索
    search()
  }
}, { immediate: true })

onBeforeMount(() => {
  refreshInterval.value = setInterval(() => {
    checkAndUpdateDates()
    search()
  }, 300000) // 5分钟刷新一次
})

onBeforeUnmount(() => {
  // 清除自动刷新
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})

// 设置开始时间
function setStartTime(value: Date) {
  form.setFieldValue('startTime', value)
}

// 设置结束时间
function setEndTime(value: Date) {
  form.setFieldValue('endTime', value)
}

// 重置时间
function resetDates() {
  initializeDates()
  search()
}
</script>

<template>
  <div class="w-full" :class="{ light: !isDark, dark: isDark }">
    <form v-show="dashboardStore.isSearchVisible" class="mb-2 flex justify-end gap-4 px-8" @submit="search">
      <FDatePicker name="startTime" label="开始时间" :date-props="{ showTime: true, hourFormat: '24' }" />
      <FDatePicker name="endTime" label="结束时间" :date-props="{ showTime: true, hourFormat: '24' }" />
      <Button class="mt-6" size="small" icon="pi pi-search" type="submit" :loading="loading" />
      <Button class="mt-6" size="small" icon="pi pi-refresh" @click="resetDates" />
    </form>

    <div class="grid grid-cols-24 mb-1 mt-1 items-center justify-items-center gap-1">
      <div class="col-span-3 w-full flex flex-col items-center justify-center gap-1 py-4 panel">
        <span>开班时间</span>
        <span class="text-4xl text-blue-500 font-600">{{ oee?.actualPlanTime }} h</span>
      </div>
      <div class="col-span-3 w-full flex flex-col items-center justify-center gap-1 py-4 panel">
        <span>运机时间</span>
        <span class="text-4xl text-green-500 font-600">{{ oee?.runTime }} h</span>
      </div>
      <div class="col-span-3 w-full flex flex-col items-center justify-center gap-1 py-4 panel">
        <span>停机时间</span>
        <span class="text-4xl text-red-500 font-600">{{ oee?.stopTime }} h</span>
      </div>
      <div class="col-span-4 w-full flex flex-col items-center justify-center gap-1 py-4 panel">
        <span>标准生产数量 | 实际生产数量</span>
        <span class="text-4xl text-purple-500 font-600">{{ oee?.planBoard }} | {{ oee?.actualBoard }}</span>
      </div>
      <div class="col-span-5 w-full flex flex-col items-center justify-center gap-1 py-4 panel">
        <span>标准点位数量 | 实际点位数量</span>
        <span class="text-4xl text-purple-500 font-600">{{ oee?.planBoardPoints }} | {{ oee?.actualBoardPoints }}</span>
      </div>
      <div class="col-span-2 w-full flex flex-col items-center justify-center gap-1 py-4 panel">
        <span>不良品数</span>
        <span class="text-4xl text-orange-500 font-600">{{ oee?.defectCount || 0 }}</span>
      </div>
      <div class="col-span-4 w-full flex flex-col items-center justify-center gap-1 py-4 panel">
        <span>换线次数 | 换线时间</span>
        <span class="text-4xl text-yellow-500 font-600">{{ oee?.changeoverNum || 0 }} | {{ oee?.changeoverTime }}</span>
      </div>
    </div>

    <div class="grid grid-cols-4 mb-1 items-center justify-items-center gap-1">
      <div class="w-full flex flex-col items-center justify-center gap-1 py-4 panel">
        <span>目标运转率 | 实际运转率</span>
        <span class="text-4xl text-blue-500 font-600">{{ oee?.availabilityTarget }}% | {{ oee?.availability }}%</span>
      </div>
      <div class="w-full flex flex-col items-center justify-center gap-1 py-4 panel">
        <span>目标有效生产率 | 实际有效生产率</span>
        <span class="text-4xl text-purple-500 font-600">{{ oee?.performanceTarget }}% | {{ oee?.performance }}%</span>
      </div>
      <div class="w-full flex flex-col items-center justify-center gap-1 py-4 panel">
        <span>目标良品率 | 实际良品率</span>
        <span class="text-4xl text-green-500 font-600">{{ oee?.qualityTarget }}% | {{ oee?.quality }}%</span>
      </div>
      <div class="w-full flex flex-col items-center justify-center gap-1 py-4 panel">
        <span>目标OEE | 实际OEE</span>
        <span class="text-4xl text-orange-500 font-600">{{ oee?.oeeTarget }}% | {{ oee?.oee }}%</span>
      </div>
    </div>

    <div grid grid-cols-10 mb-1 gap-1>
      <!-- 设备ct 单轨 -->
      <div v-if="productionLineType === 'singleTrack'" class="col-span-6 h-[220px] p-4 panel">
        <h5>设备CT</h5>
        <div class="h-[180px] flex">
          <div class="flex-1 pl-4">
            <template v-if="deviceChunks.length">
              <Carousel :value="deviceChunks" :num-visible="1" :num-scroll="1" :circular="false">
                <template #item="{ data }">
                  <div class="grid grid-cols-5 place-items-center gap-4">
                    <DeviceCtCard v-for="device in data" :key="device.code" :device="device" />
                  </div>
                </template>
              </Carousel>
            </template>
            <template v-else>
              <div class="h-full flex items-center justify-center">
                <span class="text-gray-500">暂无设备</span>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- 设备ct 双轨 -->
      <div v-if="productionLineType === 'dualTrack'" class="col-span-6 h-[220px] p-4 panel">
        <h5 class="mb-2">
          设备CT
        </h5>
        <div class="h-[calc(100%-2rem)] flex flex-col overflow-hidden">
          <div v-for="[trackName, devices] in devicesByTrack" :key="trackName" class="h-1/2 flex items-center">
            <div class="flex-none text-primary font-600">
              {{ trackName }}
            </div>
            <div class="flex flex-1 gap-1 overflow-x-auto">
              <DualTrackDeviceCard
                v-for="device in devices"
                :key="device.code"
                :device="device"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 不良类型 -->
      <div class="col-span-4 h-[220px] p-4 panel">
        <h5>不良类型</h5>
        <DefectTypeChart v-if="defectTypes" :data="defectTypes" />
      </div>
    </div>

    <div grid grid-cols-10 mb-1 gap-1>
      <!-- 每小时产出 -->
      <div class="col-span-6 h-[230px] p-4 panel">
        <h5>每小时产出</h5>
        <HourlyOutputChart :data="hourlyOutput" />
      </div>

      <!-- top5异常 -->
      <div class="col-span-4 h-[230px] p-4 panel">
        <h5>TOP5异常</h5>
        <Top5DefectsChart :data="topAlarms" />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 隐藏滚动条但保持可滚动 */
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* 添加圆角和透明背景样式 */
.light .panel {
  border-radius: 8px;
  background-color: transparent;
  backdrop-filter: blur(4px);
}

.light .panel {
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.container.light {
  background-color: var(--surface-ground, #f5f5f5);
}
</style>
