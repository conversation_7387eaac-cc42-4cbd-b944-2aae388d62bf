<script setup lang="ts">
import { useDark } from '@vueuse/core'
import { computed } from 'vue'

const props = defineProps<{
  // 实际值
  actual: number
  // 理论值
  theoretical: number
  // 设备信息
  deviceInfo: {
    type: string
    code: string
    trackName: string
  }
  // 瓶颈
  bottleneck: boolean
}>()

const isDark = useDark()

const chartOption = computed(() => {
  const textColor = isDark.value ? '#E5EAF3' : '#666'

  return {
    tooltip: {
      formatter: '{b} : {c}s',
    },
    title: [
      {
        text: props.deviceInfo.type,
        left: 'center',
        top: '15%',
        textStyle: {
          fontSize: 14,
          color: props.bottleneck ? '#F56C6C' : '#67C23A',
        },
      },
      {
        text: `${props.deviceInfo.code}`,
        left: 'center',
        top: '0%',
        textStyle: {
          fontSize: 14,
          color: textColor,
        },
      },
    ],
    series: [{
      type: 'gauge',
      center: ['50%', '70%'],
      radius: '70%',
      startAngle: 200,
      endAngle: -20,
      min: 0,
      max: Math.max(props.actual, props.theoretical) * 1.2,
      itemStyle: {
        color: props.bottleneck ? '#F56C6C' : '#67C23A',
      },
      progress: {
        show: true,
        roundCap: true,
        width: 8,
      },
      pointer: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          width: 8,
        },
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      anchor: {
        show: false,
      },
      detail: {
        valueAnimation: true,
        fontSize: 16,
        offsetCenter: [0, '20%'],
        formatter: '{value}s',
        color: 'inherit',
      },
      data: [{
        value: props.actual,
      }],
    }],
  }
})
</script>

<template>
  <div class="h-[140px] w-[150px]">
    <e-charts :option="chartOption" />
  </div>
</template>
