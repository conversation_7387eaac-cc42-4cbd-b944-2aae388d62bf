<script setup lang="ts">
import type { DatePickerProps } from 'primevue'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  label: string
  dateProps?: DatePickerProps
}>()
const { value, errorMessage } = useField<Date | Array<Date> | Array<Date | null> | undefined | null>(props.name)
</script>

<template>
  <LabelFormItem :label="label" :name="name">
    <DatePicker v-model="value" class="w-full md:w-56" :input-id="props.name" fluid show-icon date-format="yy-mm-dd" v-bind="dateProps" :invalid="errorMessage ? true : false">
      <template #inputicon="slotProps">
        <i class="pi pi-clock" @click="slotProps.clickCallback" />
      </template>
    </DatePicker>
  </LabelFormItem>
</template>
