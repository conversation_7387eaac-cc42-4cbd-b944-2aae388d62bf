<script setup lang="ts">
import { useDark } from '@vueuse/core'
import { computed } from 'vue'
import type { DefectTypeResult } from '~/api/analyze/type'

const props = defineProps<{
  data: DefectTypeResult
}>()

const isDark = useDark()

// 定义颜色数组，每个系列对应一个颜色
const colors = [
  { start: '#FF9800', end: '#FFB74D' },
  { start: '#4CAF50', end: '#81C784' },
  { start: '#2196F3', end: '#64B5F6' },
  { start: '#9C27B0', end: '#BA68C8' },
  // 可以根据需要添加更多颜色
]

const chartOption = computed(() => {
  const textColor = isDark.value ? '#E5EAF3' : '#666'
  const splitLineColor = isDark.value ? 'rgba(84,89,104,0.3)' : '#DDD'
  const axisLineColor = isDark.value ? '#626675' : '#999'

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: props.data.headMaps.map(item => item.name),
      textStyle: {
        color: textColor,
        fontSize: 14,
      },
    },
    dataset: {
      dimensions: props.data.dimensions,
      source: props.data.data,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '25%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        interval: 0,
        width: 40,
        overflow: 'break',
        lineHeight: 12,
        color: textColor,
        fontSize: 12,
      },
      axisLine: {
        lineStyle: {
          color: axisLineColor,
        },
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      name: '数量',
      minInterval: 1,
      min: 0,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: splitLineColor,
        },
      },
      nameTextStyle: {
        color: textColor,
        fontSize: 12,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        formatter: '{value}',
        color: textColor,
        fontSize: 14,
      },
    },
    series: props.data.headMaps.map((item, index) => ({
      name: item.name,
      type: item.type,
      barWidth: '25%',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: colors[index % colors.length].start,
          }, {
            offset: 1,
            color: colors[index % colors.length].end,
          }],
        },
        borderRadius: [4, 4, 0, 0],
      },
      label: {
        show: true,
        position: 'top',
        fontSize: 14,
        color: textColor,
      },
    })),
  }
})
</script>

<template>
  <div class="h-[180px]">
    <e-charts :option="chartOption" />
  </div>
</template>
