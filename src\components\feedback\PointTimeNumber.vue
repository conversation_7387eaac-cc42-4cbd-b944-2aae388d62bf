<script setup lang="ts">
const props = defineProps<{
  name: string
}>()

const { value, errorMessage } = useField<number>(() => props.name)

// 小时输入 (0-23)
const hours = ref<number>(0)
// 分钟输入 (0-59)
const minutes = ref<number>(0)

// 初始化时根据 value 拆分小时和分钟
watchEffect(() => {
  if (typeof value.value === 'number' && !Number.isNaN(value.value)) {
    hours.value = Math.floor(value.value / 60)
    minutes.value = value.value % 60
  }
})

// 当小时或分钟变化时，更新 value
watch([hours, minutes], ([h, m]) => {
  value.value = h * 60 + m
})
</script>

<template>
  <div class="max-w-125 flex items-center gap-4">
    <!-- 小时输入 -->
    <div class="flex flex-col gap-2">
      <label>小时</label>
      <InputNumber
        v-model="hours"
        :min="0"
        :max="23"
        :step="1"
        show-buttons
        input-class="w-full"
        class="w-25"
      />
    </div>

    <!-- 分隔符 -->
    <div class="mt-6 text-xl">
      :
    </div>

    <!-- 分钟输入 -->
    <div class="flex flex-col gap-2">
      <label>分钟</label>
      <InputNumber
        v-model="minutes"
        :min="0"
        :max="59"
        :step="5"
        show-buttons
        input-class="w-full"
        class="w-25"
      />
    </div>
  </div>

  <ErrorMsg :error-message="errorMessage" />
</template>
