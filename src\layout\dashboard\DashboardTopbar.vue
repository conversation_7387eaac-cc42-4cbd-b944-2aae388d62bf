<script setup>
import { useDark } from '@vueuse/core'
import screenfull from 'screenfull'
import { useRoute, useRouter } from 'vue-router'
import AppConfigurator from '../AppConfigurator.vue'
import logoDark from '~/assets/logo_dark.png'
import logoLight from '~/assets/logo_light.png'
import { useDashboardStore } from '~/stores/dashboard'
import { useLayoutStore } from '~/stores/layout'

const route = useRoute()
const { toggleDarkMode, isDark } = useLayoutStore()
const { onProductlineToggle, setActiveLineItem, activeLineItem } = useProductline()
const dashboardStore = useDashboardStore()
const router = useRouter()

// 切换主题
const dark = useDark()
// 获取logo
const logo = computed(() => (dark.value ? logoLight : logoDark))
const isFullscreen = ref(false)

// 全屏
function toggleFullscreen() {
  if (screenfull.isEnabled) {
    screenfull.toggle()
  }
}

// 回退
function goBack() {
  router.push('/')
}

onBeforeMount(() => {
  setActiveLineItem(route.params.code)
})
</script>

<template>
  <div class="layout-topbar">
    <div class="layout-topbar-logo-container">
      <button class="layout-menu-button layout-topbar-action" @click="onProductlineToggle">
        <i class="pi pi-bars" />
      </button>
      <img :src="logo" alt="Logo" class="ml-4 h-12">
    </div>
    <div class="w-full flex justify-center text-3xl text-primary font-500">
      <div class="flex flex-row items-center gap-10">
        <div class="flex items-center gap-2">
          <div
            v-if="dashboardStore.exception"
            class="flex animate-pulse items-center rounded bg-red-100 px-3 py-1 text-sm text-red-800 font-bold"
          >
            <i class="pi pi-exclamation-triangle mr-1" />
            {{ dashboardStore.exception }}
          </div>
          <span class="text-xl text-gray-500 font-800">{{ dashboardStore.currentTime }}</span>
        </div>
        <div class="text-4xl font-bold tracking-wide">
          {{ activeLineItem?.name }} OEE看板
        </div>
        <div class="flex flex-col items-center">
          <div
            v-for="item in dashboardStore.currentProduct"
            :key="item.productModel"
            class="flex items-center gap-2 text-sm text-gray-500 font-semibold"
          >
            <span v-if="dashboardStore.currentProduct.length > 1" class="rounded bg-gray-100 px-2 py-1 dark:bg-gray-800">{{ item.trackName }}</span>
            <span class="rounded bg-blue-50 px-2 py-1 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
              {{ item.productModel }}
            </span>
            <span
              v-if="item.sample === 'sample'"
              class="rounded-full bg-yellow-500 px-2 py-0.5 text-xs text-black font-bold shadow-sm"
            >
              样品
            </span>
          </div>
        </div>
        <div class="flex flex-col items-center" />
      </div>
    </div>

    <div class="layout-topbar-actions">
      <div class="layout-config-menu">
        <button type="button" class="layout-topbar-action" @click="dashboardStore.toggleSearch">
          <i class="pi pi-search" />
        </button>
        <button type="button" class="layout-topbar-action" @click="toggleDarkMode">
          <i class="pi" :class="isDark ? 'pi-moon' : 'pi-sun'" />
        </button>
        <button type="button" class="layout-topbar-action" @click="toggleFullscreen">
          <i class="pi" :class="isFullscreen ? 'pi-window-minimize' : 'pi-window-maximize'" />
        </button>
        <button type="button" class="layout-topbar-action" @click="goBack">
          <i class="pi pi-arrow-left" />
        </button>
        <div class="relative">
          <button
            v-styleclass="{ selector: '@next', enterFromClass: 'hidden', enterActiveClass: 'animate-scale-in animate-duration-100', leaveToClass: 'hidden', leaveActiveClass: 'animate-fade-out animate-duration-100', hideOnOutsideClick: true }"
            type="button"
            class="layout-topbar-action layout-topbar-action-highlight"
          >
            <i class="pi pi-palette" />
          </button>
          <AppConfigurator />
        </div>
      </div>

      <button
        v-styleclass="{ selector: '@next', enterFromClass: 'hidden', enterActiveClass: 'animate-scale-in animate-duration-100', leaveToClass: 'hidden', leaveActiveClass: 'animate-fade-out animate-duration-100', hideOnOutsideClick: true }"
        class="layout-topbar-action layout-topbar-menu-button"
      >
        <i class="pi pi-ellipsis-v" />
      </button>

      <div class="layout-topbar-menu hidden lg:block">
        <div class="layout-topbar-menu-content" />
      </div>
    </div>
  </div>
</template>
