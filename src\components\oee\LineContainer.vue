<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { analyzeApi } from '~/api/analyze'
import type { OeeResult } from '~/api/analyze/type'
import { TriggerRecordApi } from '~/api/feedback/trigger'
import { useAnalyticsSearchSchema } from '~/pages/dashboard/schema'

const props = defineProps<{
  code: string
  startTime: Date
  endTime: Date
}>()

interface LineStatus {
  anomaliesName: string
  triggerTime: Date | null
}

const router = useRouter()

// 状态管理
const form = useAnalyticsSearchSchema()
const loading = ref<boolean>(false)

// 数据状态
const oee = ref<OeeResult>()
const lineStatus = ref<LineStatus>({
  anomaliesName: '正常',
  triggerTime: null,
})

// API 调用封装
const loadData = form.handleSubmit(async (values) => {
  loading.value = true
  try {
    const res = await analyzeApi.getLineOee(values)
    oee.value = res
  }
  finally {
    loading.value = false
  }
})

const loadProductionLineStatus = form.handleSubmit(async (values) => {
  loading.value = true
  try {
    const res = await TriggerRecordApi.latestOpenException(values.code)
    if (res) {
      lineStatus.value = {
        anomaliesName: res.anomaliesName,
        triggerTime: res.triggerTime,
      }
    }
    else {
      lineStatus.value = {
        anomaliesName: '正常',
        triggerTime: null,
      }
    }
  }
  finally {
    loading.value = false
  }
})

// 生命周期和监听器
watch(props, (newProps) => {
  if (newProps) {
    // 设置产品线
    form.setFieldValue('code', newProps.code)
    form.setFieldValue('startTime', newProps.startTime)
    form.setFieldValue('endTime', newProps.endTime)
    // 加载数据
    loadData()
    // 加载线体状态
    loadProductionLineStatus()
  }
}, { immediate: true, deep: true })

// 点击线体跳转到具体产线数据
function navigateToLineDetail() {
  router.push({ name: 'dashboard', params: { code: props.code } })
}
</script>

<template>
  <div class="w-full" :class="{ light: !isDark, dark: isDark }">
    <div class="grid grid-cols-[20%_80%] grid-rows-1 gap-1 rounded-lg from-gray-700 via-gray-900 to-black bg-gradient-to-r pt-2 shadow-lg">
      <!-- 点击线体跳转 -->
      <div
        class="row-span-2 flex flex-col transform cursor-pointer items-center justify-center transition-transform panel hover:scale-105"
        @click="navigateToLineDetail"
      >
        <span class="text-2xl text-white">线体</span>
        <span class="text-3xl text-blue-500 font-bold">{{ props.code }}</span>
        <div class="mt-1 flex flex-col items-center">
          <span v-if="lineStatus.triggerTime" class="block text-gray-300">
            {{ new Date(lineStatus.triggerTime).toLocaleString([], { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' }) }}
          </span>
          <span :class="lineStatus.anomaliesName === '正常' ? 'text-green-400' : 'text-red-400'">
            {{ lineStatus.anomaliesName }}
          </span>
        </div>
      </div>
      <div class="grid grid-cols-12 gap-1">
        <div class="col-span-2 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">开班时间</span>
          <span class="text-xl text-primary font-bold">{{ oee?.actualPlanTime }} h</span>
        </div>
        <div class="col-span-2 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">运机时间</span>
          <span class="text-xl text-primary font-bold">{{ oee?.runTime }} h</span>
        </div>
        <div class="col-span-2 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">停机时间</span>
          <span class="text-xl text-primary font-bold">{{ oee?.stopTime }} h</span>
        </div>
        <div class="col-span-3 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">标准生产数量 | 实际生产数量</span>
          <span class="text-xl text-primary font-bold">{{ oee?.planBoard }} | {{ oee?.actualBoard }}</span>
        </div>
        <div class="col-span-1 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">不良品数</span>
          <span class="text-xl text-primary font-bold">{{ oee?.defectCount || 0 }}</span>
        </div>
        <div class="col-span-2 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">换线次数 | 换线时间</span>
          <span class="text-xl text-primary font-bold">{{ oee?.changeoverNum || 0 }} | {{ oee?.changeoverTime }}</span>
        </div>
      </div>

      <div class="grid grid-cols-4 gap-1">
        <div class="w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">目标运转率 | 实际运转率</span>
          <span class="text-xl text-primary font-bold">{{ oee?.availabilityTarget }}% | {{ oee?.availability }}%</span>
        </div>
        <div class="w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">目标有效生产率 | 实际有效生产率</span>
          <span class="text-xl text-primary font-bold">{{ oee?.performanceTarget }}% | {{ oee?.performance }}%</span>
        </div>
        <div class="w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">目标良品率 | 实际良品率</span>
          <span class="text-xl text-primary font-bold">{{ oee?.qualityTarget }}% | {{ oee?.quality }}%</span>
        </div>
        <div class="w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">目标OEE | 实际OEE</span>
          <span class="text-xl text-primary font-bold">{{ oee?.oeeTarget }}% | {{ oee?.oee }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.panel {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px;
  transition: transform 0.3s ease;
}
.panel:hover {
  transform: scale(1.05);
}
</style>
