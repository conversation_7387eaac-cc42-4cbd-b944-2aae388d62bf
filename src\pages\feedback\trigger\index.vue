<script setup lang="ts">
import { formatDate } from '@vueuse/core'
import { type DataTablePageEvent, useConfirm } from 'primevue'
import Create from './Create.vue'
import NoticeUserDialog from './NoticeUserDialog.vue'
import { useFeedbackTriggerRecordSearchForm } from './schema'
import SolutionList from './SolutionList.vue'
import type { FeedbackTriggerRecordWithNoticeUser, NoticeUser } from '~/api/feedback/trigger/type'
import { TriggerRecordApi } from '~/api/feedback/trigger'
import type { PageData } from '~/api/common/type'

// 响应式状态
const total = ref(0)
const loading = ref(false)
const data = ref<FeedbackTriggerRecordWithNoticeUser[]>([])
const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})

// 弹窗控制
const modals = reactive({
  create: false,
  edit: false,
  solutionList: false,
  noticeUsers: false,
})

// 解决方案记录相关
const selectedRecordId = ref<string>()
// 通知用户相关
const selectedNoticeUsers = ref<NoticeUser[]>([])

// 搜索相关
const { handleSubmit } = useFeedbackTriggerRecordSearchForm()
const confirm = useConfirm()

// 数据获取
const fetchData = handleSubmit(async (searchParams) => {
  try {
    loading.value = true
    const res = await TriggerRecordApi.page({ searchParams, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

// 分页处理
function handlePageChange(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  fetchData()
}

// 关闭触发
async function handleCloseTrigger(id: string, event: Event) {
  confirm.require({
    group: 'delete',
    target: event.currentTarget as HTMLElement,
    message: '确认关闭？',
    accept: async () => {
      await TriggerRecordApi.closeException(id)
      success('关闭成功')
      fetchData()
    },
  })
}

// 查看解决方案记录
function handleViewSolutions(id: string) {
  selectedRecordId.value = id
  modals.solutionList = true
}

// 查看通知用户
function handleViewNoticeUsers(id: string) {
  const record = data.value.find(item => item.id === id)
  selectedNoticeUsers.value = record?.noticeUsers || []
  modals.noticeUsers = true
}

onMounted(fetchData)
</script>

<template>
  <PageContainer>
    <!-- 操作工具栏 -->
    <ToolbarAction>
      <Button icon="pi pi-plus" label="添加触发" outlined @click="modals.create = true" />
    </ToolbarAction>

    <CardContainer>
      <!-- 数据表格 -->
      <DataTable
        :value="data" lazy paginator data-key="id" :rows="pageData.pageSize" :total-records="total"
        @page="handlePageChange"
      >
        <Column field="id" header="ID" />
        <Column field="lineCode" header="线体编码" />
        <Column field="anomaliesCode" header="异常大类">
          <template #body="slotProps">
            {{ formatDict(slotProps.data.anomaliesCode, 'ABNORMAL_CATEGORY') }}
          </template>
        </Column>
        <Column field="anomaliesStartTime" header="异常开始时间">
          <template #body="slotProps">
            {{ slotProps.data.anomaliesStartTime ? formatDate(slotProps.data.anomaliesStartTime, 'YYYY-MM-DD HH:mm:ss')
              : '' }}
          </template>
        </Column>
        <Column field="anomaliesDetail" header="异常明细" />
        <Column field="triggerTime" header="触发时间">
          <template #body="slotProps">
            {{ slotProps.data.triggerTime ? formatDate(slotProps.data.triggerTime, 'YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </Column>
        <Column field="triggerUserName" header="触发人" />
        <Column field="triggerClose" header="异常是否关闭">
          <template #body="slotProps">
            <span :class="slotProps.data.triggerClose ? 'text-green-600' : 'text-red-600'">
              <Tag
                :value="slotProps.data.triggerClose ? '已关闭' : '未关闭'"
                :severity="slotProps.data.triggerClose ? 'success' : 'warn'"
              />
            </span>
          </template>
        </Column>
        <Column field="triggerCloseTime" header="异常关闭时间">
          <template #body="slotProps">
            {{ slotProps.data.triggerCloseTime ? formatDate(slotProps.data.triggerCloseTime, 'YYYY-MM-DD HH:mm:ss') : ''
            }}
          </template>
        </Column>
        <!-- 操作列 -->
        <Column header="操作">
          <template #body="{ data: rowData }">
            <ActionGroupCol>
              <Button
                v-tooltip.top="'查看解决方案记录'" icon="pi pi-eye" size="small" outlined severity="info" label="查看解决方案"
                @click="handleViewSolutions(rowData.id)"
              />
              <Button
                v-tooltip.top="'查看通知用户'" icon="pi pi-users" size="small" outlined severity="success" label="通知人"
                @click="handleViewNoticeUsers(rowData.id)"
              />
              <Button
                v-if="!rowData.triggerClose" icon="pi pi-times" size="small" outlined severity="secondary"
                label="关闭触发" @click="handleCloseTrigger(rowData.id, $event)"
              />
            </ActionGroupCol>
          </template>
        </Column>

        <!-- 加载状态 -->
        <template #empty>
          <TableEmpty />
        </template>
      </DataTable>
    </CardContainer>

    <!-- 创建弹窗 -->
    <Create v-model:open="modals.create" @save="fetchData" />

    <!-- 解决方案记录列表弹窗 -->
    <SolutionList v-model:open="modals.solutionList" :record-id="selectedRecordId" />

    <!-- 通知用户列表弹窗 -->
    <NoticeUserDialog v-model:open="modals.noticeUsers" :notice-users="selectedNoticeUsers" />

    <!-- <Edit
      :id="editId"
      v-model:open="modals.edit"
      @save="fetchData"
    /> -->
  </PageContainer>
</template>
