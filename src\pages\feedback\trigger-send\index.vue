<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import { formatDate } from '@vueuse/core'
import { useConfirm } from 'primevue'
import { useFeedbackTriggerSendSearchForm } from './schema'
import Create from './Create.vue'
import Edit from './Edit.vue'
import { TriggerSendApi } from '~/api/feedback/trigger-send'
import type { FeedbackTriggerSendWithId } from '~/api/feedback/trigger-send/type'
import { error, success } from '~/composables/toast'

// 页面状态
const loading = ref(false)
const data = ref<FeedbackTriggerSendWithId[]>([])
const total = ref(0)
const selectedItems = ref<FeedbackTriggerSendWithId[]>([])

// 分页数据
const pageData = reactive({
  pageNumber: 0,
  pageSize: 10,
})

// 对话框状态
const modals = reactive({
  create: false,
  edit: false,
})
const editId = ref<string>()

// 搜索表单
const searchForm = useFeedbackTriggerSendSearchForm()
const confirm = useConfirm()

// 获取数据
const fetchData = searchForm.handleSubmit(async (searchParams) => {
  try {
    loading.value = true
    const res = await TriggerSendApi.page({ searchParams, pageData })
    data.value = res.list
    total.value = res.total
  }
  catch (err) {
    console.error('获取数据失败:', err)
    error('获取数据失败')
  }
  finally {
    loading.value = false
  }
})

// 分页处理
function handlePageChange(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  fetchData()
}

// 删除单个记录
async function handleDelete(id: string) {
  confirm.require({
    message: '确定要删除这条记录吗？',
    header: '确认删除',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '删除',
      severity: 'danger',
    },
    accept: async () => {
      try {
        await TriggerSendApi.delete(id)
        success('删除成功')
        fetchData()
      }
      catch (err) {
        console.error('删除失败:', err)
        error('删除失败')
      }
    },
  })
}

// 批量删除
async function handleBatchDelete() {
  if (selectedItems.value.length === 0) {
    error('请选择要删除的记录')
    return
  }

  confirm.require({
    message: `确定要删除选中的 ${selectedItems.value.length} 条记录吗？`,
    header: '确认批量删除',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '删除',
      severity: 'danger',
    },
    accept: async () => {
      try {
        const ids = selectedItems.value.map(item => item.id)
        await TriggerSendApi.batchDelete(ids)
        success('批量删除成功')
        selectedItems.value = []
        fetchData()
      }
      catch (err) {
        console.error('批量删除失败:', err)
        error('批量删除失败')
      }
    },
  })
}

// 手动发送
async function handleSend(id: string) {
  confirm.require({
    message: '确定要手动发送这条记录吗？',
    header: '确认发送',
    icon: 'pi pi-question-circle',
    group: 'confirm',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '发送',
      severity: 'success',
    },
    accept: async () => {
      try {
        await TriggerSendApi.send(id)
        success('发送成功')
        fetchData()
      }
      catch (err) {
        console.error('发送失败:', err)
        error('发送失败')
      }
    },
  })
}

// 批量发送
async function handleBatchSend() {
  const unsentItems = selectedItems.value.filter(item => !item.sendStatus)
  if (unsentItems.length === 0) {
    error('请选择未发送的记录')
    return
  }

  confirm.require({
    message: `确定要发送选中的 ${unsentItems.length} 条记录吗？`,
    header: '确认批量发送',
    icon: 'pi pi-question-circle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '发送',
      severity: 'success',
    },
    accept: async () => {
      try {
        const ids = unsentItems.map(item => item.id)
        await TriggerSendApi.batchSend(ids)
        success('批量发送成功')
        selectedItems.value = []
        fetchData()
      }
      catch (err) {
        console.error('批量发送失败:', err)
        error('批量发送失败')
      }
    },
  })
}

// 打开编辑对话框
function openEditDialog(id: string) {
  editId.value = id
  modals.edit = true
}

// 保存后刷新数据
function handleSave() {
  fetchData()
}

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<template>
  <PageContainer>
    <!-- 搜索表单 -->
    <form class="mb-6 px-4 space-y-4" @submit="fetchData">
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-4 md:grid-cols-2">
        <FInput name="triggerRecordId" label="触发记录ID" />
        <FSelect
          name="sendStatus" label="发送状态" :options="[
            { label: '全部', value: undefined },
            { label: '已发送', value: true },
            { label: '未发送', value: false },
          ]"
        />
        <FDatePicker name="expectedSendStartTime" label="预期发送开始时间" :date-props="{ showTime: true }" />
        <FDatePicker name="expectedSendEndTime" label="预期发送结束时间" :date-props="{ showTime: true }" />
      </div>
      <div class="flex justify-end gap-2">
        <Button label="查询" icon="pi pi-search" type="submit" :loading="loading" />
        <Button label="重置" icon="pi pi-refresh" severity="secondary" outlined @click="searchForm.resetForm()" />
        <Button label="新建" icon="pi pi-plus" outlined @click="modals.create = true" />
        <Button
          label="批量删除" icon="pi pi-trash" severity="danger" outlined :disabled="selectedItems.length === 0"
          @click="handleBatchDelete"
        />
        <Button
          label="批量发送" icon="pi pi-send" severity="success" outlined
          :disabled="selectedItems.filter(item => !item.sendStatus).length === 0" @click="handleBatchSend"
        />
      </div>
    </form>

    <CardContainer>
      <!-- 数据表格 -->
      <DataTable
        v-model:selection="selectedItems" :value="data" lazy paginator data-key="id" :rows="pageData.pageSize"
        :total-records="total" :loading="loading" @page="handlePageChange"
      >
        <Column selection-mode="multiple" />
        <Column field="id" header="ID" sortable />
        <Column field="triggerRecordId" header="触发记录ID" sortable />
        <Column field="expectedSendTime" header="预期发送时间" sortable>
          <template #body="slotProps">
            {{ slotProps.data.expectedSendTime ? formatDate(slotProps.data.expectedSendTime, 'YYYY-MM-DD HH:mm:ss') : ''
            }}
          </template>
        </Column>
        <Column field="sendTime" header="发送时间" sortable>
          <template #body="slotProps">
            {{ slotProps.data.sendTime ? formatDate(slotProps.data.sendTime, 'YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </Column>
        <Column field="sendStatus" header="发送状态" sortable>
          <template #body="slotProps">
            <Tag
              :value="slotProps.data.sendStatus ? '已发送' : '未发送'"
              :severity="slotProps.data.sendStatus ? 'success' : 'warning'"
            />
          </template>
        </Column>
        <Column field="sendResult" header="发送结果">
          <template #body="slotProps">
            <div
              v-if="slotProps.data.sendResult"
              v-tooltip.top="slotProps.data.sendResult"
              class="max-w-xs"
            >
              <p class="line-clamp-2 text-sm text-gray-700 leading-tight dark:text-gray-300">
                {{ slotProps.data.sendResult }}
              </p>
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </Column>
        <Column header="操作">
          <template #body="slotProps">
            <div class="flex gap-2">
              <Button icon="pi pi-pencil" size="small" outlined @click="openEditDialog(slotProps.data.id)" />
              <Button
                v-if="!slotProps.data.sendStatus" icon="pi pi-send" size="small" severity="success" outlined
                @click="handleSend(slotProps.data.id)"
              />
              <Button
                icon="pi pi-trash" size="small" severity="danger" outlined
                @click="handleDelete(slotProps.data.id)"
              />
            </div>
          </template>
        </Column>
      </DataTable>
    </CardContainer>

    <!-- 创建对话框 -->
    <Create v-model:open="modals.create" @save="handleSave" />

    <!-- 编辑对话框 -->
    <Edit :id="editId" v-model:open="modals.edit" @save="handleSave" />
  </PageContainer>
</template>
