import type { AnomaliesClassificationCreateParam, AnomaliesClassificationSearchParam, AnomaliesClassificationUpdateParam, AnomaliesClassificationWithId } from './type'
import type { PageList, Pageable } from '~/api/common/type'
import { kyDelete, kyGet, kyPost, kyPut } from '~/utils/request'

export const feedbackConfigApi = {
  create: (param: AnomaliesClassificationCreateParam) => kyPost('anomaliesClassification/add', param),
  page: (param: Pageable<Partial<AnomaliesClassificationSearchParam>>) => kyPost('anomaliesClassification/page', param).json<PageList<AnomaliesClassificationWithId>>(),
  update: (id: string, param: AnomaliesClassificationUpdateParam) => kyPut(`anomaliesClassification/${id}`, param),
  delete: (id: string) => kyDelete(`anomaliesClassification/${id}`),
  get: (id: string) => kyGet(`anomaliesClassification/${id}`).json<AnomaliesClassificationWithId>(),
  enable: (id: string, status: boolean) => kyPut(`anomaliesClassification/${id}/enable`, '', { enable: status }),
  findOne: (lineCode: string, anomaliesCode: string) => kyGet('anomaliesClassification/findByLineCodeAndAnomaliesCode', { lineCode, anomaliesCode }).json<AnomaliesClassificationWithId>(),
}
