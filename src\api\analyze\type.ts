import type { LineType } from '../line/types'

// 分析查询参数接口
export interface AnalyzeQuery {
  code: string // 线体代码
  startTime?: Date // 开始时间
  endTime?: Date // 结束时间
  productModel?: string // 产品型号（可选）
}

// OEE结果接口
export interface OeeResult {
  changeoverNum: number // 换线次数
  changeoverTime: string // 换线时间
  planTime: string // 计划时间
  actualPlanTime: string // 实际计划时间
  runTime: string // 运行时间
  stopTime: string // 停止时间
  actualStopTime: string // 实际停止时间
  actualBoard: string // 实际生产数量
  planBoard: string // 理论生产数量
  actualBoardPoints: string // 实际产品点数
  planBoardPoints: string // 计划产品点数
  goodBoard: string // 良品数量
  availability: string // 稼动率
  availabilityTarget: string // 稼动率目标
  performance: string // 运行效率
  performanceTarget: string // 运行效率目标
  defectCount: string // 不良数量
  quality: string // 良品率
  qualityTarget: string // 良品率目标
  oee: string // OEE值
  oeeTarget: string // OEE目标
}

// 设备CT信息接口
export interface DeviceCtInfo {
  code: string // 设备代码
  type: string // 设备类型
  actualCt: number // 实际CT
  theoreticalCt: number // 理论CT
  bottleneck: boolean // 是否是瓶颈
  trackName: string // 轨道名称
}

// 不良类型信息接口
export interface DefectTypeInfo {
  type: string // 不良类型
  count: number // 数量
}

/**
 * 不良类型信息
 */
export interface DefectTypeResult {
  headMaps: Heads[] // 数据头
  dimensions: string[] // 维度
  data: Map<string, string>[] // 数据
}

interface Heads {
  type: string
  name: string
}

// 异常信息接口
export interface AlarmInfo {
  type: string // 异常类型
  name: string // 异常名称
  num: number // 异常次数
}

// 线体换线信息接口
export interface LineChangeoverInfo {
  changeoverNum: number // 换线次数
}

/** 设备每小时产出 */
export interface HourlyOutputList {
  /** 每小时产量列表 */
  actualOutputs: HourlyOutput[]
  /** 每小时理论产量列表 */
  theoreticalOutputs: HourlyOutput[]
  /** 当前产品理论产量 */
  currentProductTheoreticalOutput: number
}

/** 小时产量 */
export interface HourlyOutput {
  /** 时间 */
  time: string
  /** 数量 */
  count: number
}

/** 线体信息 */
export interface ProductionLineResult {
  productionLineInfos: ProductionLineInfo[]
  productionLineType: LineType
}

/** 产品信息 */
export interface ProductionLineInfo {
  // 产品型号
  productModel: string
  // 轨道编码
  trackName: string
  // 样品
  sample: ProductionSample
}

export enum ProductionSample {
  sample,
  notsample,
}

// 分析查询参数
export type AnalyzeQueryParam = AnalyzeQuery
