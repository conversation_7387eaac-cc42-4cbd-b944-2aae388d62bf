<script setup lang="ts">
import { useDark } from '@vueuse/core'
import { computed } from 'vue'
import type { HourlyOutputList } from '~/api/analyze/type'

const props = defineProps<{
  data?: HourlyOutputList
}>()

const isDark = useDark()

const chartOption = computed(() => {
  const hasData = props.data?.actualOutputs && props.data.actualOutputs.length > 0
  const textColor = isDark.value ? '#E5EAF3' : '#666'
  const splitLineColor = isDark.value ? 'rgba(84,89,104,0.3)' : '#DDD'
  const axisLineColor = isDark.value ? '#626675' : '#999'

  const sortedActualOutputs = hasData
    ? [...props.data.actualOutputs].sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime())
    : []

  const sortedTheoreticalOutputs = hasData
    ? [...props.data.theoreticalOutputs].sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime())
    : []

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#ccc',
          width: 1,
          type: 'dashed',
        },
      },
    },
    legend: {
      data: ['实际产出', '理论产出'],
      right: '10%',
      textStyle: {
        color: textColor,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '25%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: hasData
        ? sortedActualOutputs.map((item) => {
            const time = new Date(item.time)
            const endTime = new Date(time.getTime() + 3600000)
            return `${time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })} - ${endTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`
          })
        : [],
      axisLine: {
        lineStyle: {
          color: axisLineColor,
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: textColor,
      },
    },
    yAxis: {
      type: 'value',
      name: '数量',
      nameTextStyle: {
        color: textColor,
      },
      min: 0,
      minInterval: 1,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: splitLineColor,
        },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        formatter: '{value}',
        color: textColor,
      },
    },
    series: [{
      name: '实际产出',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: {
        color: '#409EFF',
      },
      lineStyle: {
        width: 3,
      },
      label: {
        show: hasData,
        position: 'top',
        formatter: '{c}',
        fontSize: 14,
        color: textColor,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(64,158,255,0.2)',
          }, {
            offset: 1,
            color: 'rgba(64,158,255,0)',
          }],
        },
      },
      data: hasData ? sortedActualOutputs.map(item => item.count) : [],
    }, {
      name: '理论产出',
      type: 'line',
      // step: 'start',
      smooth: false,
      symbol: 'none',
      itemStyle: {
        color: '#4CAF50',
      },
      lineStyle: {
        type: 'dashed',
        width: 2,
      },
      label: {
        show: hasData,
        position: 'top',
        formatter: '{c}',
        fontSize: 14,
        color: textColor,
      },
      data: hasData ? sortedTheoreticalOutputs.map(item => item.count) : [],
    }],
    graphic: [
      ...(hasData
        ? [{
            type: 'text',
            left: '45%',
            top: '4%',
            style: {
              text: `当前理论产出: ${props.data?.currentProductTheoreticalOutput}`,
              textAlign: 'center',
              fill: '#4CAF50',
              fontSize: 15,
              fontWeight: 800,
            },
          }]
        : [{
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '暂无数据',
              fontSize: 14,
              fill: textColor,
            },
          }]),
    ],
  }
})
</script>

<template>
  <div class="h-[190px]">
    <e-charts :option="chartOption" />
  </div>
</template>
