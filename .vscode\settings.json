{
  "files.associations": {
    "*.css": "postcss"
  },

  // Enable the ESlint flat config support
  "eslint.useFlatConfig": true,
  "eslint.debug": true,
  // Disable the default formatter
  "prettier.enable": false,
  "editor.formatOnSave": true,
  // "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",

  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },

  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    { "rule": "style/*", "severity": "off" },
    { "rule": "*-indent", "severity": "off" },
    { "rule": "*-spacing", "severity": "off" },
    { "rule": "*-spaces", "severity": "off" },
    { "rule": "*-order", "severity": "off" },
    { "rule": "*-dangle", "severity": "off" },
    { "rule": "*-newline", "severity": "off" },
    { "rule": "*quotes", "severity": "off" },
    { "rule": "*semi", "severity": "off" }
  ],

  // The following is optional.
  // It's better to put under project setting `.vscode/settings.json`
  // to avoid conflicts with working with different eslint configs
  // that does not support all formats.
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml"
  ],
  "cSpell.customDictionaries": {
    "custom-dictionary-workspace": {
      "name": "custom-dictionary-workspace",
      "path": "${workspaceFolder}/.cspell/custom-dictionary-workspace.txt",
      "addWords": true,
      "scope": "workspace"
    }
  },
  "cSpell.ignorePaths": [
    "package-lock.json",
    "node_modules",
    "vscode-extension",
    ".git/{info,lfs,logs,refs,objects}/**",
    ".git/{index,*refs,*HEAD}",
    ".vscode",
    ".vscode-insiders",
    ".cspell",
    ".husky",
    "settings.json",
    ".config",
    "~/.config/**"
  ],
  "i18n-ally.localesPaths": ["src/i18n", "src/i18n/lang"],

  // 文件折叠配置
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    // Vue 相关文件
    "*.vue": "${capture}.vue.ts,${capture}.vue.js,${capture}.vue.scss,${capture}.vue.css,${capture}.vue.less,${capture}.stories.ts,${capture}.stories.js,${capture}.test.ts,${capture}.test.js,${capture}.spec.ts,${capture}.spec.js",

    // TypeScript/JavaScript 相关文件
    "*.ts": "${capture}.js,${capture}.d.ts,${capture}.js.map,${capture}.min.js,${capture}.test.ts,${capture}.test.js,${capture}.spec.ts,${capture}.spec.js",
    "*.js": "${capture}.js.map,${capture}.min.js,${capture}.d.ts,${capture}.test.js,${capture}.spec.js",

    // 样式文件
    "*.css": "${capture}.css.map,${capture}.min.css,${capture}.scss,${capture}.sass,${capture}.less",
    "*.scss": "${capture}.css,${capture}.css.map,${capture}.min.css",
    "*.less": "${capture}.css,${capture}.css.map,${capture}.min.css",

    // 配置文件
    "package.json": "package-lock.json,yarn.lock,pnpm-lock.yaml,.npmrc,.yarnrc,.yarnrc.yml,npm-shrinkwrap.json",
    "tsconfig.json": "tsconfig.*.json,tsconfig.build.json,tsconfig.node.json",
    "vite.config.*": "vite.config.ts.timestamp-*",
    "tailwind.config.*": "tailwind.config.ts.timestamp-*",
    "eslint.config.*": ".eslintrc.*,.eslintignore",
    ".env": ".env.*,.env.local,.env.development,.env.production,.env.staging,.env.test",

    // README 和文档
    "README.md": "README.*,readme.*,CHANGELOG.md,CHANGELOG.*,HISTORY.md,LICENSE,LICENSE.*,CONTRIBUTING.md",

    // Git 相关
    ".gitignore": ".gitattributes,.gitmodules,.gitmessage,.mailmap,.git-blame*",

    // Docker 相关
    "Dockerfile": "Dockerfile.*,.dockerignore,docker-compose.*,docker-compose.override.yml",

    // 其他配置文件
    "*.config.js": "*.config.ts,*.config.mjs,*.config.cjs",
    "*.config.ts": "*.config.js,*.config.mjs,*.config.cjs",
    ".editorconfig": ".prettierrc.*,.prettierignore",

    // 组件相关文件（针对Vue组件）
    "index.vue": "components.d.ts,auto-imports.d.ts",
    "App.vue": "app.html,app.css,app.scss,app.less"
  },
  "[vue]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  }
}
