import { toTypedSchema } from '@vee-validate/zod'

const TriggerRecordSearchSchema = toTypedSchema(
  z.object({
    anomaliesCode: z.string().optional(),
    anomaliesName: z.string().optional(),
  }),
)

const TriggerRecordCreateSchema = toTypedSchema(
  z.object({
    anomaliesCode: z.string().min(1),
    anomaliesName: z.string().optional(),
    lineCode: z.string().min(1),
    anomaliesDetail: z.string().min(1),
    anomaliesStartTime: z.date(),
    noticeUsers: z.array(z.object({
      noticeUserId: z.string().min(1),
      noticeUserName: z.string().optional(),
    })).min(1, '至少选择一个通知用户').default([]),
  }),
)

export function useFeedbackTriggerRecordSearchForm() {
  const form = useForm({
    validationSchema: TriggerRecordSearchSchema,
  })
  return form
}

export function useFeedbackTriggerRecordCreateForm() {
  const form = useForm({
    validationSchema: TriggerRecordCreateSchema,
  })
  return form
}
