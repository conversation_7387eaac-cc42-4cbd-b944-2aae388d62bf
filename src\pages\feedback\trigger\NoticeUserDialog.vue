<script setup lang="ts">
import type { NoticeUser } from '~/api/feedback/trigger/type'

defineProps<{
  noticeUsers?: NoticeUser[]
}>()

// 响应式状态
const open = defineModel<boolean>('open', { required: true })

// 关闭对话框
function closeDialog() {
  open.value = false
}
</script>

<template>
  <Dialog
    v-model:visible="open"
    modal
    header="通知用户列表"
    :style="{ width: 'min(600px, 90vw)' }"
  >
    <div class="space-y-4">
      <!-- 通知用户列表 -->
      <div v-if="noticeUsers && noticeUsers.length > 0" class="space-y-2">
        <div class="text-sm text-gray-700 font-medium">
          共 {{ noticeUsers.length }} 位通知用户：
        </div>

        <!-- 用户列表容器 -->
        <div class="max-h-80 overflow-auto border-1 rounded-md p-3 border-surface">
          <div class="space-y-2">
            <div v-for="user in noticeUsers" :key="user.noticeUserId">
              <Chip
                :label="user.noticeUserName || user.noticeUserId"
                class="w-full"
              >
                <div class="w-full flex items-center justify-between">
                  <span class="text-sm font-medium">{{ user.noticeUserName || '未知用户' }}</span>
                  <span class="text-xs text-gray-500">{{ user.noticeUserId }}</span>
                </div>
              </Chip>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex flex-col items-center justify-center py-8 text-gray-500">
        <i class="pi pi-users mb-2 text-4xl" />
        <span>暂无通知用户</span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="flex justify-end">
        <Button
          label="关闭"
          severity="secondary"
          @click="closeDialog"
        />
      </div>
    </template>
  </Dialog>
</template>
