<script setup lang="ts">
import { useConfirm } from 'primevue'
import type { DataTablePageEvent } from 'primevue/datatable'
import Create from './Create.vue'
import Edit from './Edit.vue'
import { useFeedbackConfigSearchForm } from './schema'
import type { PageData } from '~/api/common/type'
import { feedbackConfigApi } from '~/api/feedback/config'
import type { AnomaliesClassificationWithId } from '~/api/feedback/config/type'
import { success } from '~/composables/toast'

// 响应式状态
const total = ref(0)
const loading = ref(false)
const dataList = ref<AnomaliesClassificationWithId[]>([])
const editId = ref<string>()
const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})
const expandedRows = ref({})

// 弹窗控制
const modals = reactive({
  create: false,
  edit: false,
})

// 搜索相关
const { handleSubmit, resetForm } = useFeedbackConfigSearchForm()
const confirm = useConfirm()

// 数据获取
const fetchData = handleSubmit(async (searchParams) => {
  try {
    loading.value = true
    const res = await feedbackConfigApi.page({ searchParams, pageData })
    dataList.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

// 分页处理
function handlePageChange(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  fetchData()
}

// 打开编辑页
function handleOpenEdit(id: string) {
  editId.value = id
  modals.edit = true
}

// 删除确认
function handleConfirmDelete(id: string, event: Event) {
  confirm.require({
    group: 'delete',
    target: event.currentTarget as HTMLElement,
    message: '确认删除？',
    accept: async () => {
      await feedbackConfigApi.delete(id)
      success('删除成功')
      dataList.value = dataList.value.filter(o => o.id !== id)
    },
  })
}

// 切换启用状态
async function handleToggleEnable(id: string, enable: boolean) {
  try {
    loading.value = true
    await feedbackConfigApi.enable(id, enable)
    success('更新成功')
    await fetchData()
  }
  finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})
</script>

<template>
  <PageContainer>
    <!-- 操作栏 -->
    <form class="mb-6 flex items-end justify-between gap-4 px-4" @submit="fetchData" @search="fetchData">
      <div class="flex gap-2">
        <FDictSelect name="lineCode" label="所属线体" code="LINE_CODE" />
        <FDictSelect name="anomaliesCode" label="异常分类" code="ABNORMAL_CATEGORY" />
      </div>
      <div class="flex gap-2">
        <Button label="查询" icon="pi pi-search" type="submit" :loading="loading" />
        <Button label="重置" icon="pi pi-eraser" severity="secondary" outlined @click="resetForm()" />
        <Button
          icon="pi pi-plus"
          label="新建配置"
          outlined
          @click="modals.create = true"
        />
      </div>
    </form>

    <!-- 数据表格 -->
    <DataTable
      v-model:expanded-rows="expandedRows"
      :value="dataList"
      :lazy="true"
      :paginator="true"
      :rows="pageData.pageSize"
      :total-records="total"
      data-key="id"
      responsive-layout="stack"
      @page="handlePageChange"
    >
      <!-- <template #header>
        <div class="flex flex-wrap justify-end gap-2">
          <Button text icon="pi pi-plus" label="Expand All" @click="expandAll" />
          <Button text icon="pi pi-minus" label="Collapse All" @click="collapseAll" />
        </div>
      </template> -->
      <Column expander style="width: 5rem" />
      <Column field="id" header="ID" />
      <Column field="anomaliesCode" header="所属线体">
        <template #body="slotProps">
          {{ formatDict(slotProps.data.lineCode, 'LINE_CODE') }}
        </template>
      </Column>
      <Column field="anomaliesCode" header="异常大类">
        <template #body="slotProps">
          {{ formatDict(slotProps.data.anomaliesCode, 'ABNORMAL_CATEGORY') }}
        </template>
      </Column>
      <Column field="enable" header="是否启用">
        <template #body="{ data: rowData }">
          <InputSwitch
            :model-value="rowData.enable"
            :loading="loading"
            @update:model-value="(value) => handleToggleEnable(rowData.id, value)"
          />
        </template>
      </Column>

      <!-- 操作列 -->
      <Column header="操作" width="150">
        <template #body="{ data: rowData }">
          <div class="flex gap-2">
            <Button
              icon="pi pi-pencil"
              size="small"
              outlined
              @click="handleOpenEdit(rowData.id)"
            />
            <Button
              icon="pi pi-trash"
              size="small"
              outlined
              severity="danger"
              @click="handleConfirmDelete(rowData.id, $event)"
            />
          </div>
        </template>
      </Column>

      <template #expansion="slotProps">
        <div class="p-4">
          <DataTable :value="slotProps.data.responseConfig">
            <Column field="pointTime" header="响应时间(分钟)" sortable />
            <Column field="respondentId" header="处理人">
              <template #body="{ data }">
                <div class="flex flex-wrap gap-1">
                  <Tag
                    v-for="responder in data.responders"
                    :key="responder.respondentId"
                    severity="info"
                    rounded
                  >
                    {{ responder.respondentName || responder.respondentId }}
                  </Tag>
                </div>
              </template>
            </Column>
            <Column field="reporterId" header="抄送人">
              <template #body="{ data }">
                <div class="flex flex-wrap gap-1">
                  <Tag
                    v-for="reporter in data.reporters"
                    :key="reporter.reporterId"
                    severity="info"
                    rounded
                  >
                    {{ reporter.reportName || reporter.reporterId }}
                  </Tag>
                </div>
              </template>
            </Column>
          </DataTable>
        </div>
      </template>

      <!-- 加载状态 -->
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>

    <!-- 创建弹窗 -->
    <Create
      v-model:open="modals.create"
      @save="fetchData"
    />
    <Edit
      :id="editId"
      v-model:open="modals.edit"
      @save="fetchData"
    />
  </PageContainer>
</template>
