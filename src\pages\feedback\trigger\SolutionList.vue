<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { PageData } from '~/api/common/type'
import { SolutionApi } from '~/api/feedback/solution'
import type { FeedbackTriggerSolution } from '~/api/feedback/solution/type'

interface Props {
  open: boolean
  recordId?: string
}

interface Emits {
  (e: 'update:open', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式状态
const loading = ref(false)
const solutionList = ref<FeedbackTriggerSolution[]>([])
const total = ref(0)
const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})

// 计算属性
const visible = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

// 获取解决方案列表
async function fetchSolutions() {
  if (!props.recordId)
    return

  try {
    loading.value = true
    const searchParams = {
      triggerRecordId: props.recordId,
    }
    const res = await SolutionApi.page({ searchParams, pageData })
    solutionList.value = res.list
    total.value = res.total
  }
  catch (err) {
    console.error('获取解决方案列表失败:', err)
    solutionList.value = []
    total.value = 0
  }
  finally {
    loading.value = false
  }
}

// 分页处理
function handlePageChange(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  fetchSolutions()
}

// 对话框显示时加载数据
function onShow() {
  if (props.recordId) {
    fetchSolutions()
  }
}

// 关闭对话框
function closeDialog() {
  visible.value = false
  // 重置数据
  solutionList.value = []
  total.value = 0
  pageData.pageNumber = 0
}

// 解决方案详情相关
const detailDialogVisible = ref(false)
const selectedSolution = ref<FeedbackTriggerSolution | null>(null)

// 查看解决方案详情
// function viewSolutionDetail(solution: FeedbackTriggerSolution) {
//   selectedSolution.value = solution
//   detailDialogVisible.value = true
// }
</script>

<template>
  <Dialog
    v-model:visible="visible"
    modal
    header="解决方案记录"
    :style="{ width: '80vw', maxWidth: '1200px' }"
    @show="onShow"
    @hide="closeDialog"
  >
    <div class="space-y-4">
      <!-- 记录信息 -->
      <div v-if="recordId" class="rounded-lg bg-blue-50 p-4">
        <div class="flex items-center gap-2">
          <i class="pi pi-info-circle text-blue-600" />
          <span class="text-blue-800 font-medium">触发记录ID: {{ recordId }}</span>
        </div>
      </div>

      <!-- 数据表格 -->
      <DataTable
        :value="solutionList"
        :lazy="true"
        :paginator="true"
        :rows="pageData.pageSize"
        :total-records="total"
        :loading="loading"
        data-key="id"
        responsive-layout="stack"
        @page="handlePageChange"
      >
        <Column field="id" header="ID" width="100" />
        <Column field="solution" header="解决方案">
          <template #body="{ data }">
            <div class="max-w-md">
              <p class="line-clamp-3 text-sm">
                {{ data.solution }}
              </p>
            </div>
          </template>
        </Column>
        <Column field="solverName" header="解决人" width="120" />
        <Column field="solveTime" header="解决时间" width="180">
          <template #body="{ data }">
            {{ new Date(data.solveTime).toLocaleString() }}
          </template>
        </Column>
        <Column field="submitted" header="提交状态" width="120">
          <template #body="{ data }">
            <Tag
              :value="data.submitted ? '已提交' : '未提交'"
              :severity="data.submitted ? 'success' : 'warning'"
            />
          </template>
        </Column>
        <Column field="submitTime" header="提交时间" width="180">
          <template #body="{ data }">
            <span v-if="data.submitted && data.submitTime">
              {{ new Date(data.submitTime).toLocaleString() }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </Column>
        <Column header="操作" width="100">
          <!-- <template #body="{ data }">
            <Button
              v-tooltip.top="'查看详情'"
              icon="pi pi-eye"
              size="small"
              outlined
              severity="info"
              @click="viewSolutionDetail(data)"
            />
          </template> -->
        </Column>

        <!-- 空状态 -->
        <template #empty>
          <div class="flex flex-col items-center justify-center py-8">
            <i class="pi pi-inbox mb-4 text-4xl text-gray-400" />
            <p class="text-gray-600">
              暂无解决方案记录
            </p>
          </div>
        </template>
      </DataTable>
    </div>

    <!-- 对话框底部 -->
    <template #footer>
      <div class="flex justify-end">
        <Button
          label="关闭"
          icon="pi pi-times"
          severity="secondary"
          @click="closeDialog"
        />
      </div>
    </template>
  </Dialog>

  <!-- 解决方案详情对话框 -->
  <Dialog
    v-model:visible="detailDialogVisible"
    modal
    header="解决方案详情"
    :style="{ width: '60vw', maxWidth: '800px' }"
  >
    <div v-if="selectedSolution" class="space-y-4">
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div class="space-y-1">
          <label class="text-sm text-gray-600 font-medium">解决人</label>
          <p class="text-gray-900">
            {{ selectedSolution.solverName }}
          </p>
        </div>
        <div class="space-y-1">
          <label class="text-sm text-gray-600 font-medium">解决时间</label>
          <p class="text-gray-900">
            {{ new Date(selectedSolution.solveTime).toLocaleString() }}
          </p>
        </div>
        <div class="space-y-1">
          <label class="text-sm text-gray-600 font-medium">提交状态</label>
          <Tag
            :value="selectedSolution.submitted ? '已提交' : '未提交'"
            :severity="selectedSolution.submitted ? 'success' : 'warning'"
          />
        </div>
        <div v-if="selectedSolution.submitted && selectedSolution.submitTime" class="space-y-1">
          <label class="text-sm text-gray-600 font-medium">提交时间</label>
          <p class="text-gray-900">
            {{ new Date(selectedSolution.submitTime).toLocaleString() }}
          </p>
        </div>
      </div>
      <div class="space-y-1">
        <label class="text-sm text-gray-600 font-medium">解决方案内容</label>
        <div class="rounded-lg bg-gray-50 p-4">
          <p class="whitespace-pre-wrap text-gray-900">
            {{ selectedSolution.solution }}
          </p>
        </div>
      </div>
    </div>

    <template #footer>
      <Button
        label="关闭"
        icon="pi pi-times"
        severity="secondary"
        @click="detailDialogVisible = false"
      />
    </template>
  </Dialog>
</template>
