import { defineStore } from 'pinia'
import type { ProductionLineInfo } from '~/api/analyze/type'

interface DashboardState {
  isSearchVisible: boolean
  currentProduct: ProductionLineInfo[]
  currentTime: string
  exception: string
}

export const useDashboardStore = defineStore('dashboard', {
  state: (): DashboardState => ({
    isSearchVisible: false,
    currentProduct: [],
    currentTime: '',
    exception: '',
  }),
  actions: {
    // 切换搜索框
    toggleSearch() {
      this.isSearchVisible = !this.isSearchVisible
    },
    // 设置当前产品
    setCurrentProduct(product: ProductionLineInfo[]) {
      this.currentProduct = product
    },
    // 设置当前时间
    setCurrentTime(time: string) {
      this.currentTime = time
    },
    // 设置当前异常
    setException(exception: string) {
      this.exception = exception
    },
  },
})
