<script setup lang="ts">
import { useDark } from '@vueuse/core'
import { computed } from 'vue'
import type { AlarmInfo } from '~/api/analyze/type'

const props = defineProps<{
  data?: AlarmInfo[]
}>()

const isDark = useDark()

const chartOption = computed(() => {
  const hasData = props.data && props.data.length > 0
  const textColor = isDark.value ? '#E5EAF3' : '#666'
  const splitLineColor = isDark.value ? 'rgba(84,89,104,0.3)' : '#DDD'
  const axisLineColor = isDark.value ? '#626675' : '#999'

  const names = hasData ? props.data?.map(item => item.name).reverse() ?? [] : []
  const values = hasData ? props.data?.map(item => item.num).reverse() ?? [] : []

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: ['异常时长'],
      textStyle: {
        color: textColor,
        fontSize: 14,
      },
    },
    grid: {
      left: '3%',
      right: '8%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      name: '时长',
      nameLocation: 'end',
      nameTextStyle: {
        color: textColor,
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: splitLineColor,
        },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: textColor,
        fontSize: 14,
      },
    },
    yAxis: {
      type: 'category',
      data: names, // Use dynamic data
      axisLabel: {
        color: textColor,
        fontSize: 14,
      },
      axisLine: {
        lineStyle: {
          color: axisLineColor,
        },
      },
      axisTick: {
        show: false,
      },
    },
    series: [{
      name: '异常时长',
      type: 'bar',
      barWidth: '60%',
      data: values,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [{
            offset: 0,
            color: isDark.value ? '#FFC107' : '#FFC107',
          }, {
            offset: 1,
            color: isDark.value ? '#B28704' : '#FFE082',
          }],
        },
        borderRadius: [0, 4, 4, 0],
      },
      label: {
        show: true,
        position: 'right',
        fontSize: 14,
        color: textColor,
      },
    }],
    graphic: !hasData
      ? [{
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '暂无数据',
            fontSize: 14,
            fill: textColor,
          },
        }]
      : undefined,
  }
})
</script>

<template>
  <div class="h-[190px]">
    <e-charts :option="chartOption" />
  </div>
</template>
