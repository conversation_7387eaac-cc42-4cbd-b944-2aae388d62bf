import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { z } from '~/utils/zext'

const feedbackHandleSchema = toTypedSchema(
  z.object({
    solution: z.string().min(1, '请输入解决方案'),
    solveTime: z.date({
      required_error: '请选择解决时间',
    }),
  }),
)

export function useFeedbackHandleForm() {
  const form = useForm({
    validationSchema: feedbackHandleSchema,
  })
  return form
}
