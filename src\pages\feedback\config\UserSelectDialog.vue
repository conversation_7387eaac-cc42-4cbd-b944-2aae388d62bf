<script setup lang="ts">
import { HTTPError } from 'ky'
import type { TreeNode } from 'primevue/treenode'
import { wxServerApi } from '~/api/wx-server'
import type { TreeNode as DeptNode, User } from '~/api/wx-server/types'

const props = defineProps<{
  initialSelectedUsers?: User[]
}>()

const emit = defineEmits<{
  (e: 'select', users: User[]): void
}>()

// 响应式状态
const treeLoading = ref(false)
const tableLoading = ref(false)
const open = defineModel<boolean>('open', { required: true })
const searchKeyword = ref('')
const selectedDeptId = ref<number | null>(null)
const nodes = ref<DeptNode[]>([])
const users = ref<User[]>([])
const userList = ref<User[]>([])

// 获取部门列表
async function fetchDepartments() {
  try {
    treeLoading.value = true
    nodes.value = await wxServerApi.departMentList()
  }
  catch (ex) {
    if (ex instanceof HTTPError) {
      error(ex.message)
    }
  }
  finally {
    treeLoading.value = false
  }
}

// 根据部门ID获取用户列表
async function loadUsers(deptId: number) {
  try {
    tableLoading.value = true
    users.value = await wxServerApi.userListByDept(deptId)
  }
  catch (ex) {
    if (ex instanceof HTTPError) {
      error(ex.message)
    }
  }
  finally {
    tableLoading.value = false
  }
}

// 初始化表单
function initializeForm() {
  userList.value = props.initialSelectedUsers ? [...props.initialSelectedUsers] : []
}

// 添加单个用户
function addUser(user: User) {
  if (!userList.value.some(u => u.userid === user.userid))
    userList.value = [...userList.value, user]
}

// 从已选用户中移除指定用户
function removeSelectedUser(index: number) {
  userList.value.splice(index, 1)
}

// 清空所有已选用户
function clearSelectedUsers() {
  if (userList.value.length > 0)
    userList.value = []
}

// 保存选中用户
function handleSave() {
  emit('select', userList.value)
  open.value = false
}

// 部门节点选择处理
function handleNodeSelect(node: TreeNode) {
  const deptId = Number(node.id)
  selectedDeptId.value = deptId
  loadUsers(deptId)
}

// 过滤用户列表
const filteredUsers = computed(() => {
  const keyword = searchKeyword.value.trim().toLowerCase()
  return keyword
    ? users.value.filter(u =>
        u.name.toLowerCase().includes(keyword)
        || (u.userid && u.userid.includes(keyword)),
      )
    : users.value
})

onMounted(() => {
  fetchDepartments()
})
</script>

<template>
  <Dialog
    v-model:visible="open"
    modal
    header="选择响应人"
    :style="{ width: 'min(1000px, 90vw)' }"
    @show="initializeForm"
  >
    <div class="space-y-6">
      <!-- 搜索区域 -->
      <div class="flex justify-start gap-4">
        <InputText
          v-model.trim="searchKeyword"
          placeholder="搜索姓名或工号"
          class="flex-1"
        />
      </div>

      <!-- 内容区域 -->
      <div class="h-[60vh] flex flex-col gap-4 md:flex-row">
        <!-- 左侧：部门和用户列表 -->
        <div class="flex flex-1 flex-col gap-4 md:flex-row">
          <Tree
            :value="nodes"
            :loading="treeLoading"
            class="w-full overflow-auto border-r-1 md:w-72"
            selection-mode="single"
            @node-select="handleNodeSelect"
          />

          <div class="flex-1 overflow-hidden">
            <div v-if="!selectedDeptId" class="p-4 text-center">
              请先选择部门
            </div>
            <DataTable
              v-else
              :value="filteredUsers"
              :loading="tableLoading"
              data-key="userid"
              scrollable
              scroll-height="flex"
              paginator
              :rows="10"
              :rows-per-page-options="[5, 10, 20]"
            >
              <Column field="name" header="姓名" sortable />
              <Column field="userid" header="工号" sortable />
              <Column header="操作">
                <template #body="{ data }">
                  <Button
                    icon="pi pi-user-plus"
                    severity="secondary"
                    size="small"
                    :disabled="userList.some(u => u.userid === data.userid)"
                    @click="addUser(data)"
                  />
                </template>
              </Column>

              <template #empty>
                <div class="py-4 text-center">
                  {{ searchKeyword ? '未找到匹配用户' : '该部门暂无用户' }}
                </div>
              </template>

              <template #loading>
                <div class="flex justify-center p-4">
                  <ProgressSpinner style="width: 40px; height: 40px" />
                </div>
              </template>
            </DataTable>
          </div>
        </div>

        <!-- 右侧：已选用户区域 -->
        <div class="h-full w-full flex flex-col border-1 rounded-md p-2 border-surface md:w-72">
          <div class="mb-2 flex items-center justify-between">
            <span class="text-sm font-semibold">已选用户</span>
            <div class="flex items-center gap-2">
              <span class="text-xs">{{ userList.length }}人</span>
              <Button
                v-if="userList.length > 0"
                icon="pi pi-trash"
                text
                size="small"
                severity="danger"
                @click="clearSelectedUsers"
              />
            </div>
          </div>
          <div class="flex-1 overflow-auto p-1">
            <template v-if="userList.length > 0">
              <div v-for="(user, index) in userList" :key="user.userid" class="mb-1">
                <Chip
                  :label="user.name"
                  class="w-full justify-between"
                  removable
                  @remove="removeSelectedUser(index)"
                >
                  <span class="text-sm">{{ user.name }}</span>
                  <span class="text-xs">{{ user.userid }}</span>
                </Chip>
              </div>
            </template>
            <div v-else class="p-2 text-center">
              点击+号添加用户
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-end gap-4">
        <Button
          label="取消"
          severity="secondary"
          @click="open = false"
        />
        <Button
          label="确认选择"
          severity="primary"
          @click="handleSave"
        />
      </div>
    </div>
  </Dialog>
</template>
